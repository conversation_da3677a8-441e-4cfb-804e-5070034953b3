/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

/**
 * Base model with Gaco-specific configuration
 */
export interface BaseGacoModel {}
/**
 * Request model for sales staging data tagging configuration
 */
export interface SalesStagingProducerTagRequest {
  storeId: string;
  fields: string[];
  tagRule: {
    [k: string]: string[];
  };
  overrideVendor: boolean;
  updateVendorIfNull: boolean;
}

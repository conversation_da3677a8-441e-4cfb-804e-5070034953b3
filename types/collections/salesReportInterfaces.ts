/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

export type SalesReportStatus = "pending" | "accepted" | "sent" | "revise";

/**
 * Base model config for all models
 */
export interface BaseModelConfig {}
export interface ProducerLevelData {
  producerId: string;
  subtotal: number;
  storeTotalGrossPayout: number;
  producerTotalGrossPayout: number;
  producerDisplayName: string;
}
export interface SalesLevelDataItem {
  saleId: string;
  title: string;
  variantTitle?: string | null;
  updatedAt: string;
  subtotal: number;
  commission: number;
  producerGrossPayout: number;
}
export interface SalesReportModel {
  salesReportId: string;
  storeId: string;
  producerId: string;
  producerLevel: ProducerLevelData;
  salesLevelData: SalesLevelDataItem[];
  salesReportDate: string;
  saleIds: string[];
  title: string;
  localFilePath?: string | null;
  uri?: string | null;
  gcsPath?: string | null;
  downloadUrl?: string | null;
  createdAt: string;
  updatedAt: string;
  status: SalesReportStatus;
  templateUsed: string;
  startDate?: Date | null;
  endDate?: Date | null;
  updatedByFunction?: string;
}

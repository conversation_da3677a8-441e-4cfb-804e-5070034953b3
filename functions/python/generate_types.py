from pydantic2ts import generate_typescript_defs
from pathlib import Path

base_path = Path(__file__).parent.parent.parent / "types"

types_collections_path = base_path / "collections"

generate_typescript_defs(
  "models.application",
  f"{types_collections_path}/applicationInterfaces.ts"
)

generate_typescript_defs(
  "models.sales",
  f"{types_collections_path}/salesInterfaces.ts"
)

generate_typescript_defs(
  "models.store",
  f"{types_collections_path}/storeInterfaces.ts"
)

generate_typescript_defs(
  "models.producer",
  f"{types_collections_path}/producerInterfaces.ts"
)

generate_typescript_defs(
  "models.user_root_account",
  f"{types_collections_path}/userRootAccountInterfaces.ts"
)

generate_typescript_defs(
  "models.agreement",
  f"{types_collections_path}/agreementInterfaces.ts"
)

generate_typescript_defs(
  "models.sales_report",
  f"{types_collections_path}/salesReportInterfaces.ts"
)

types_requests_path = base_path / "requests"


generate_typescript_defs(
  "models.requests.store_requests",
  f"{types_requests_path}/storeRequestInterfaces.ts"
)

generate_typescript_defs(
  "models.requests.producer_requests",
  f"{types_requests_path}/producerRequestInterfaces.ts"
)

generate_typescript_defs(
  "models.requests.sanitization_request",
  f"{types_requests_path}/sanitizationRequestInterfaces.ts"
)

generate_typescript_defs(
  "models.requests.invoice_request",
  f"{types_requests_path}/invoiceRequestInterfaces.ts"
)

generate_typescript_defs(
  "models.requests.sales_report_request",
  f"{types_requests_path}/salesReportRequestInterfaces.ts"
)

generate_typescript_defs(
  "models.payout_account",
  f"{types_requests_path}/payoutAccountInterfaces.ts"
)

generate_typescript_defs(
  "models.requests.shopify_requests",
  f"{types_requests_path}/shopifyRequestInterfaces.ts"
)

generate_typescript_defs(
  "models.requests.agreements_requests",
  f"{types_requests_path}/agreementsInterfaces.ts"
)

generate_typescript_defs(
  "models.requests.email_requests",
  f"{types_requests_path}/emailRequestInterfaces.ts"
)

generate_typescript_defs(
  "models.requests.product_requests",
  f"{types_requests_path}/productRequestInterfaces.ts"
)

generate_typescript_defs(
  "models.requests.allocation_requests",
  f"{types_requests_path}/allocationRequestInterfaces.ts"
)

generate_typescript_defs(
  "models.requests.product_request_requests",
  f"{types_requests_path}/productRequestRequestInterfaces.ts"
)

generate_typescript_defs(
  "models.requests.tagger_requests",
  f"{types_requests_path}/SalesStagingProducerTagRequestInterfaces.ts"
)


types_responses_path = base_path / "responses"

generate_typescript_defs(
  "models.response",
  f"{types_responses_path}/responseInterfaces.ts"
)





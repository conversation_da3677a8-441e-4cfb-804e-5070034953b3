from enum import Enum
from typing import Optional, ClassVar, List, Dict, Any
from pydantic import BaseModel, model_validator
from models.base import BaseModelConfig
from gaco_framework.models import TimeStamp
from models.validators import CommissionValidatorMixin

agreement_collection = "agreements"

class AgreementStatus(str, Enum):
    DRAFT = "draft"
    NEGOTIATION = "negotiation"
    PENDING_APPROVAL = "pending_approval"
    APPROVED = "approved"
    REJECTED = "rejected"
    ACTIVE = "active"
    EXPIRED = "expired"
    TERMINATED = "terminated"

class ApprovalStatus(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"

class Role(str, Enum):
    STORE = "store"
    PRODUCER = "producer"
    SYSTEM = "system"

class NegotiationChange(BaseModelConfig):
    USER_ID_FIELD: ClassVar[str] = 'userId'
    ROLE_FIELD: ClassVar[str] = 'role'
    TIMESTAMP_FIELD: ClassVar[str] = 'timestamp'
    CHANGES_FIELD: ClassVar[str] = 'changes'
    COMMENTS_FIELD: ClassVar[str] = 'comments'

    user_id: str
    role: Role
    timestamp: TimeStamp
    changes: Dict[str, Any]  # Fields that were changed
    comments: Optional[str] = None

class ApprovalStep(BaseModelConfig):
    ROLE_FIELD: ClassVar[str] = 'role'
    APPROVER_ID_FIELD: ClassVar[str] = 'approverId'
    STATUS_FIELD: ClassVar[str] = 'status'
    COMMENTS_FIELD: ClassVar[str] = 'comments'
    TIMESTAMP_FIELD: ClassVar[str] = 'timestamp'

    role: Role
    approver_id: Optional[str] = None
    status: str = ApprovalStatus.PENDING.value
    comments: Optional[str] = None
    timestamp: Optional[TimeStamp] = None


class Agreement(BaseModelConfig, CommissionValidatorMixin):
    STORE_ID_FIELD: ClassVar[str] = 'storeId'
    PRODUCER_ID_FIELD: ClassVar[str] = 'producerId'

    STATUS_FIELD: ClassVar[str] = 'status'
    TITLE_FIELD: ClassVar[str] = 'title'

    EFFECTIVE_DATE_FIELD: ClassVar[str] = 'effectiveDate'
    EXPIRATION_DATE_FIELD: ClassVar[str] = 'expirationDate'

    COMMISSION_FIELD: ClassVar[str] = 'commission'

    DOCUMENT_URL_FIELD: ClassVar[str] = 'documentUrl'

    UPDATED_BY_FIELD: ClassVar[str] = 'updatedBy'
    UPDATED_AT_FIELD: ClassVar[str] = 'updatedAt'

    APPROVAL_WORKFLOW_FIELD: ClassVar[str] = 'approvalWorkflow'
    NEGOTIATION_HISTORY_FIELD: ClassVar[str] = 'negotiationHistory'

    PARTNERSHIP_ID_FIELD: ClassVar[str] = 'partnershipId'
    RENEWED_BY_FIELD: ClassVar[str] = 'renewedBy'
    VERSION_FIELD: ClassVar[str] = 'version'

    CREATED_BY_FIELD: ClassVar[str] = 'createdBy'
    CREATED_AT_FIELD: ClassVar[str] = 'createdAt'
    CREATED_BY_ROLE_FIELD: ClassVar[str] = 'createdByRole'
    
    # Core relationship info
    store_id: str
    producer_id: str
    partnership_id: Optional[str] = None  # Will be set when partnership is created
    
    # Contract metadata
    status: str = AgreementStatus.DRAFT.value
    title: Optional[str] = None
    
    # Dates
    effective_date: TimeStamp
    expiration_date: Optional[TimeStamp] = None
    
    # Document management
    version: str = "1.0"
    document_url: Optional[str] = None  # Cloud Storage URL
    
    # Financial terms
    commission: int
    
    # Simple approval workflow
    # there can be max 2 steps for now
    approval_workflow: List[ApprovalStep]

    # Negotiation history
    negotiation_history: Optional[List[NegotiationChange]] = []
    
    # Tracking
    created_by: str
    created_by_role: Role
    created_at: TimeStamp
    updated_by: Optional[str] = None
    updated_at: Optional[TimeStamp] = None
    renewed_by: Optional[str] = None

    @model_validator(mode='after')
    def validate_dates(self):
        if self.expiration_date is not None:
            if self.effective_date > self.expiration_date:
                raise ValueError("Effective date must be before expiration date")
        return self

from models.requests.sanitization_request import ForceAgreementRequest
from tagger.sales_tagger import SalesStagingProducerTagger, SalesStagingProducerTagRequest
from gaco_framework.auth import AuthContext
from constants.collections import (
    producer_collection,
    agreement_collection,
    sales_staging_collection,
    sales_tagging_operations_collection
)
from flows.force_active_partnership import ForceActivePartnershipFlow
from firebase_admin import firestore
from datetime import timedelta
from firebase_functions import logger
from time import sleep
import random
from models.sales import SalesStaging


def test_sales_tagging_integration(
        db, cloud_function_tester, user_with_root_account, n_sales_stagin, test_store,
        old_start_date, old_end_date
    ):


    auth_context = AuthContext(user_id=user_with_root_account.uid)

    # 1. make an agreement + producer
    result = ForceActivePartnershipFlow(db, auth_context)\
        .force_create_active_partnership(
            ForceAgreementRequest(
                store_id=test_store,
                title="test-title",
                effective_date=old_start_date,
                expiration_date=old_end_date - timedelta(days=700),
                commission=60,
                document_url="document_url",
                display_name="producer one",
                email="<EMAIL>",
                tax_a2="NL"
            )
        )

    sales_tagging_request = SalesStagingProducerTagRequest(
        store_id=test_store,
        fields=["title", "variantTitle", "variantDisplayName", "vendor"],
        tag_rule={
            result['producer_id']: ['producer_1', 'producer 1'],
        },
        override_vendor=True,
        update_vendor_if_null=True,
    )


    sleep(10)
    response = cloud_function_tester.call_function(
        "initialize_sales_tagging",
        sales_tagging_request.model_dump(),
        user_id=user_with_root_account.uid
    )

    logger.info(f"Response: {response}")
    response = response[1]

    sleep(40)
    assert "initialized successfully" in response['message'].lower()
    operation_id = response['data']['operation_id']
    operation_doc = db.collection(sales_tagging_operations_collection).document(operation_id).get()

    assert operation_doc.to_dict()['status'] == "completed"
    assert operation_doc.to_dict()['total_documents'] == 15
    assert operation_doc.to_dict()['documents_tagged'] == 5


    sample_doc_id = random.choice(n_sales_stagin[:5])
    sample_doc_data = db.collection(sales_staging_collection).document(sample_doc_id).get().to_dict()

    sales_staging = SalesStaging.model_validate(sample_doc_data)

    assert sales_staging.vendor == "producer one"
    assert sales_staging.producer_id == result['producer_id']


    # clean up
    db.collection(producer_collection).document(result['producer_id']).delete()
    db.collection(agreement_collection).document(result['agreement_id']).delete()

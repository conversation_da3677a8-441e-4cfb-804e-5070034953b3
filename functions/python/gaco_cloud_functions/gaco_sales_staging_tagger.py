"""
Sales Staging Tagger Cloud Functions

This module provides both HTTP endpoint for initialization and background task processing
for tagging sales staging data with producer information.
"""

from firebase_functions import tasks_fn, logger, options
from firebase_admin import firestore, functions
from firebase_functions.options import RetryConfig, RateLimits
from datetime import datetime

from gaco_framework import (
    gaco_endpoint,
    GacoEndpoint,
    GacoContext,
    GacoResponse,
    AuthRequirement
)
from tagger.sales_tagger import SalesStagingProducerTagger, SalesStagingProducerTagRequest
from constants.gaco_values import sales_tagger_task_queue
from constants.collections import sales_tagging_operations_collection
from constants.collections import sales_staging_collection
from models.sales import SalesStaging


# Batch processing configuration
BATCH_SIZE = 50  # Process 50 sales documents per task
MAX_CONCURRENT_TASKS = 6  # Limit concurrent tasks for cost control


@gaco_endpoint(
    region="europe-west3",
    timeout_sec=60,
    memory=options.MemoryOption.GB_1,
    require_auth=AuthRequirement.AUTHENTICATED
)
@GacoEndpoint(SalesStagingProducerTagRequest)
def initialize_sales_tagging(context: GacoContext, tag_request: SalesStagingProducerTagRequest) -> GacoResponse:
    """
    Initialize sales staging tagging process.

    This endpoint validates the request, counts total documents to process,
    and dispatches background tasks for actual processing.

    Cost-optimized: Fast initialization with minimal compute time.
    Authentication: Requires authenticated user.
    """
    # Get count of documents to process (fast query) - direct query approach
    from constants.collections import sales_staging_collection
    from models.sales import SalesStaging

    sales_staging_query = context.db.collection(sales_staging_collection).where(
        SalesStaging.STORE_ID_FIELD, '==', tag_request.store_id
    )
    sales_staging_docs = list(sales_staging_query.stream())
    total_documents = len(sales_staging_docs)

    if total_documents == 0:
        return GacoResponse(
            success=True,
            message='No sales staging documents found to process',
            code=200,
            data={
                "total_documents": 0,
                "batches_created": 0,
                "estimated_processing_time": "0 seconds"
            }
        )

    # Calculate batches needed
    total_batches = (total_documents + BATCH_SIZE - 1) // BATCH_SIZE

    # Create unique operation ID for tracking
    operation_id = f"sales_tagging_{tag_request.store_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    # Store operation metadata
    operation_data = {
        "operation_id": operation_id,
        "store_id": tag_request.store_id,
        "tag_request": tag_request.model_dump(),
        "total_documents": total_documents,
        "total_batches": total_batches,
        "batches_completed": 0,
        "documents_processed": 0,
        "documents_tagged": 0,
        "status": "initialized",
        "created_at": firestore.SERVER_TIMESTAMP,
        "created_by": context.auth_context.user_id if context.auth_context else "system"
    }

    context.db.collection(sales_tagging_operations_collection).document(operation_id).set(operation_data)

    # Dispatch background tasks for each batch
    task_queue = functions.task_queue(sales_tagger_task_queue)

    for batch_index in range(total_batches):
        task_payload = {
            'data': {
                'operation_id': operation_id,
                'batch_index': batch_index,
                'batch_size': BATCH_SIZE
            }
        }
        task_queue.enqueue(task_payload)

    logger.info(f"Initialized sales tagging operation {operation_id}: {total_batches} batches for {total_documents} documents")

    # Estimate processing time (rough calculation)
    estimated_seconds = total_batches * 10  # ~10 seconds per batch
    estimated_time = f"{estimated_seconds} seconds" if estimated_seconds < 60 else f"{estimated_seconds // 60} minutes"

    return GacoResponse(
        success=True,
        message='Sales tagging operation initialized successfully',
        code=200,
        data={
            "operation_id": operation_id,
            "total_documents": total_documents,
            "total_batches": total_batches,
            "batch_size": BATCH_SIZE,
            "estimated_processing_time": estimated_time,
            "status": "processing"
        }
    )



@tasks_fn.on_task_dispatched(
    retry_config=RetryConfig(
        min_backoff_seconds=30,
        max_backoff_seconds=120,
        max_attempts=2,
    ),
    rate_limits=RateLimits(
        max_concurrent_dispatches=MAX_CONCURRENT_TASKS,
    ),
    region="europe-west3",
    memory=options.MemoryOption.GB_2
)
def processSalesTagging(req: tasks_fn.CallableRequest) -> None:
    """
    Background task to process a batch of sales staging documents for tagging.
    
    Cost-optimized: 
    - Processes documents in batches to minimize function invocations
    - Uses moderate memory allocation (2GB)
    - Limited concurrent dispatches to control costs
    - Retry logic for reliability
    """
    operation_id = req.data.get('operation_id')
    batch_index = req.data.get('batch_index')
    batch_size = req.data.get('batch_size', BATCH_SIZE)
    
    if not operation_id or batch_index is None:
        logger.error("Missing operation_id or batch_index in task payload")
        return
    
    logger.info(f"Processing sales tagging batch {batch_index} for operation {operation_id}")
    
    try:
        # Initialize services
        db = firestore.client()
        
        # Get operation data
        operation_doc = db.collection(sales_tagging_operations_collection).document(operation_id).get()
        if not operation_doc.exists:
            logger.error(f"Operation {operation_id} not found")
            return
        
        operation_data = operation_doc.to_dict()
        tag_request_data = operation_data['tag_request']
        tag_request = SalesStagingProducerTagRequest.model_validate(tag_request_data)
        
        # Initialize tagger
        tagger = SalesStagingProducerTagger(db)
        

        try:
            # Query the collection directly
            sales_staging_query = db.collection(sales_staging_collection).where(
                SalesStaging.STORE_ID_FIELD, '==', tag_request.store_id
            )
            all_sales_docs = list(sales_staging_query.stream())
            logger.info(f"Retrieved {len(all_sales_docs)} documents for store {tag_request.store_id}")

        except Exception as e:
            logger.error(f"Error getting sales staging data: {str(e)}")
            raise

        # Calculate batch slice
        start_index = batch_index * batch_size
        end_index = min(start_index + batch_size, len(all_sales_docs))
        batch_docs = all_sales_docs[start_index:end_index]

        logger.info(f"Processing batch {batch_index}: documents {start_index} to {end_index-1}")

        if not batch_docs:
            logger.info(f"No documents in batch {batch_index} for operation {operation_id}")
            return
        
        # Process this batch
        batch_results = tagger._process_sales_documents(
            batch_docs, 
            tag_request, 
            tagger.sales_staging_collection
        )
        
        # Update operation progress (simplified non-transactional approach)
        operation_ref = db.collection(sales_tagging_operations_collection).document(operation_id)

        try:
            # Get current operation data
            operation_doc = operation_ref.get()

            if not operation_doc.exists:
                logger.error(f"Operation {operation_id} not found")
                return

            current_data = operation_doc.to_dict()

            new_batches_completed = current_data['batches_completed'] + 1
            new_documents_processed = current_data['documents_processed'] + batch_results['processed']
            new_documents_tagged = current_data['documents_tagged'] + batch_results['tagged']

            # Check if this is the last batch
            is_complete = new_batches_completed >= current_data['total_batches']

            update_data = {
                'batches_completed': new_batches_completed,
                'documents_processed': new_documents_processed,
                'documents_tagged': new_documents_tagged,
                'status': 'completed' if is_complete else 'processing',
                'updated_at': firestore.SERVER_TIMESTAMP
            }

            if is_complete:
                update_data['completed_at'] = firestore.SERVER_TIMESTAMP

            # Update the document
            operation_ref.update(update_data)

        except Exception as update_error:
            logger.error(f"Error updating operation progress: {str(update_error)}")
            is_complete = False
        
        logger.info(f"Batch {batch_index} completed for operation {operation_id}: "
                   f"processed={batch_results['processed']}, tagged={batch_results['tagged']}")
        
        if is_complete:
            logger.info(f"Sales tagging operation {operation_id} completed successfully")
        
    except Exception as e:
        logger.error(f"Error processing batch {batch_index} for operation {operation_id}: {str(e)}")
        
        # Update operation status to error
        try:
            db.collection(sales_tagging_operations_collection).document(operation_id).update({
                'status': 'error',
                'error_message': str(e),
                'error_at': firestore.SERVER_TIMESTAMP
            })
        except Exception as update_error:
            logger.error(f"Failed to update operation status: {str(update_error)}")
        
        raise  # Re-raise to trigger retry logic


